'use strict';

/**
 * Admin 2FA controller
 */
module.exports = {
  /**
   * Enable 2FA for admin user
   */
  async enable2FA(ctx) {
    try {
      const { user } = ctx.state;
      
      if (!user) {
        return ctx.unauthorized('User not authenticated');
      }

      // Generate secret for the user
      const secret = strapi.service('api::admin-2fa.otp').generateSecret();

      // Update user with 2FA enabled and secret
      await strapi.query('admin::user').update({
        where: { id: user.id },
        data: {
          twoFactorEnabled: true,
          otpSecret: secret
        }
      });

      ctx.send({
        message: '2FA has been enabled successfully',
        enabled: true
      });
    } catch (error) {
      strapi.log.error('Error enabling 2FA:', error);
      ctx.badRequest('Failed to enable 2FA');
    }
  },

  /**
   * Disable 2FA for admin user
   */
  async disable2FA(ctx) {
    try {
      const { user } = ctx.state;
      
      if (!user) {
        return ctx.unauthorized('User not authenticated');
      }

      // Update user with 2FA disabled and clear secrets
      await strapi.query('admin::user').update({
        where: { id: user.id },
        data: {
          twoFactorEnabled: false,
          otpSecret: null,
          tempOtp: null,
          otpExpiresAt: null
        }
      });

      ctx.send({
        message: '2FA has been disabled successfully',
        enabled: false
      });
    } catch (error) {
      strapi.log.error('Error disabling 2FA:', error);
      ctx.badRequest('Failed to disable 2FA');
    }
  },

  /**
   * Get 2FA status for current user
   */
  async get2FAStatus(ctx) {
    try {
      const { user } = ctx.state;
      
      if (!user) {
        return ctx.unauthorized('User not authenticated');
      }

      const adminUser = await strapi.query('admin::user').findOne({
        where: { id: user.id }
      });

      ctx.send({
        enabled: adminUser?.twoFactorEnabled || false
      });
    } catch (error) {
      strapi.log.error('Error getting 2FA status:', error);
      ctx.badRequest('Failed to get 2FA status');
    }
  },

  /**
   * Send OTP to user's email
   */
  async sendOTP(ctx) {
    try {
      const { email } = ctx.request.body;
      
      if (!email) {
        return ctx.badRequest('Email is required');
      }

      // Find admin user by email
      const adminUser = await strapi.query('admin::user').findOne({
        where: { email: email.toLowerCase() }
      });

      if (!adminUser) {
        return ctx.badRequest('User not found');
      }

      if (!adminUser.twoFactorEnabled) {
        return ctx.badRequest('2FA is not enabled for this user');
      }

      // Generate OTP
      const otp = strapi.service('api::admin-2fa.otp').generateOTP();
      const expiresAt = strapi.service('api::admin-2fa.otp').generateOTPExpiration();

      // Store OTP temporarily
      await strapi.query('admin::user').update({
        where: { id: adminUser.id },
        data: {
          tempOtp: otp,
          otpExpiresAt: expiresAt
        }
      });

      // Send OTP via email
      const emailSent = await strapi.service('api::admin-2fa.otp').sendOTPEmail(
        adminUser.email,
        otp,
        adminUser.firstname
      );

      if (!emailSent) {
        return ctx.badRequest('Failed to send OTP email');
      }

      ctx.send({
        message: 'OTP sent successfully',
        email: adminUser.email
      });
    } catch (error) {
      strapi.log.error('Error sending OTP:', error);
      ctx.badRequest('Failed to send OTP');
    }
  },

  /**
   * Verify OTP
   */
  async verifyOTP(ctx) {
    try {
      const { email, otp } = ctx.request.body;
      
      if (!email || !otp) {
        return ctx.badRequest('Email and OTP are required');
      }

      // Find admin user by email
      const adminUser = await strapi.query('admin::user').findOne({
        where: { email: email.toLowerCase() }
      });

      if (!adminUser) {
        return ctx.badRequest('User not found');
      }

      if (!adminUser.twoFactorEnabled) {
        return ctx.badRequest('2FA is not enabled for this user');
      }

      // Verify OTP
      const isValid = strapi.service('api::admin-2fa.otp').verifyOTP(
        adminUser.tempOtp,
        otp,
        adminUser.otpExpiresAt
      );

      if (!isValid) {
        return ctx.badRequest('Invalid or expired OTP');
      }

      // Clear temporary OTP
      await strapi.query('admin::user').update({
        where: { id: adminUser.id },
        data: {
          tempOtp: null,
          otpExpiresAt: null
        }
      });

      // Generate admin JWT token
      const token = strapi.service('admin::auth').createJwtToken({
        id: adminUser.id
      });

      ctx.send({
        message: 'OTP verified successfully',
        token,
        user: {
          id: adminUser.id,
          firstname: adminUser.firstname,
          lastname: adminUser.lastname,
          email: adminUser.email,
          username: adminUser.username,
          isActive: adminUser.isActive,
          blocked: adminUser.blocked,
          preferedLanguage: adminUser.preferedLanguage
        }
      });
    } catch (error) {
      strapi.log.error('Error verifying OTP:', error);
      ctx.badRequest('Failed to verify OTP');
    }
  }
};
